import 'dart:convert';
import 'dart:developer';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/strings.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:dankware/functions/common.dart';
import 'package:dankware/widgets/toast.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:dankware/widgets/transparent_fab.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class IKWYDController extends GetxController {
  var categories = <String, List<Map<String, dynamic>>>{}.obs;
  var ip = ''.obs;

  @override
  void onInit() {
    super.onInit();
    getResults();
  }

  void getResults() async {
    try {
      ip.value = await client.get(Uri.https('api64.ipify.org')).then((response) => response.body);
      log('[IKWYD] ip: ${ip.value}');
      final response = await client.get(Uri.parse('https://api.antitor.com/history/peer/?ip=${ip.value}&key=06f269480e314f9ea59cfe6c613aa787')).then((response) => response.body);
      log('[IKWYD] response: $response');
      Map<String, dynamic> responseJson = jsonDecode(response);
      categories.clear();
      for (Map<String, dynamic> content in responseJson['contents']) {
        if (categories.containsKey(content['category'])) {
          categories[content['category']]!.add(content);
        } else {
          categories[content['category']] = [content];
        }
      }
    } catch (e) {
      log('[IKWYD] $e');
      ip.value = '';
      categories.clear();
      showSnackbar(e.toString());
    }
  }
}

class IKWYD extends StatelessWidget {
  final colorSchemeController = Get.find<ColorSchemeController>();
  final controller = Get.put(IKWYDController(), permanent: trueValue);

  @override
  Widget build(BuildContext context) {
    final colorScheme = colorSchemeController.colorScheme.value;
    return Scaffold(
      extendBodyBehindAppBar: trueValue,
      appBar: transparentAppBar(MODULES.ikwyd, windowControls()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: fifteen),
        child: Obx(() => controller.categories.isEmpty
            ? Center(child: SpinKitFadingFour(color: colorScheme.fgColor, size: hundred))
            : Padding(
                padding: EdgeInsets.fromLTRB(fifteen, zero, fifteen, fifteen),
                child: ListView.separated(
                    separatorBuilder: (context, index) => SizedBox(height: fifteen),
                    itemCount: controller.categories.length,
                    itemBuilder: (context, index) {
                      final category = controller.categories.keys.elementAt(index);
                      final contents = controller.categories[category]!;
                      return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                        Text(category, style: TextStyle(color: colorScheme.fgColor, fontSize: twenty)),
                        SizedBox(height: ten),
                        for (var content in contents)
                          Column(crossAxisAlignment: CrossAxisAlignment.start, spacing: five, children: [
                            Text(content['torrent']['name'], style: TextStyle(color: colorScheme.fgColor, fontSize: ten)),
                            Text('${DateFormat('dd/MM/yyyy').format(DateTime.parse(content['endDate']))}, ${(content['torrent']['size'] / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB', style: TextStyle(color: colorScheme.fgColor2, fontSize: ten)),
                            SizedBox(height: five),
                          ])
                      ]);
                    }))),
      ),
      floatingActionButton: transparentFAB(controller.getResults, 'Refresh', Icons.refresh),
    );
  }
}
