class ROUTES {
  static const emailLookup = '/email-lookup';
  static const emailLookupResults = '/email-lookup/results';
  static const ikwyd = '/ikwyd';
  static const pythonModules = '/python-modules';
  static const randomRedditMemes = '/random-reddit-memes';
  static const settings = '/settings';
}

class MODULES {
  static const emailLookup = 'Email Lookup';
  static const ikwyd = 'IKWYD';
  static const pythonModules = 'Python Modules';
  static const randomRedditMemes = 'Random Reddit Memes';
}

class ICONS {
  static const ikwyd = 'assets/icons/ikwyd.svg';
  static const python = 'assets/icons/python.svg';
  static const reddit = 'assets/icons/reddit.svg';
}

class URLS {
  static const dankServer = 'dankware.onrender.com';
  static const python = 'http://127.0.0.1:55001';
}
