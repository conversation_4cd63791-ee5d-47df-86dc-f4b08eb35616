import 'dart:ui';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/variables.dart';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

PreferredSize transparentAppBar(String title, Map<IconData, VoidCallback?> actions) {
  return PreferredSize(
    preferredSize: Size.fromHeight(isMobile ? 56 : 38),
    child: GestureDetector(
      onPanStart: (_) async {
        if (isDesktop) {
          await windowManager.startDragging();
        }
      },
      child: AppBar(
        title: Text(title, style: const TextStyle(fontFamily: 'StintUltraExpanded', fontWeight: FontWeight.bold)),
        actions: actions.keys
            .map((icon) => IconButton(
                  icon: Icon(icon, size: isMobile ? null : fifteen),
                  onPressed: actions[icon],
                ))
            .toList(),
        forceMaterialTransparency: trueValue,
        flexibleSpace: ClipRRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ),
    ),
  );
}
