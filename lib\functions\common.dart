import 'package:dankware/constants/strings.dart';
import 'package:dankware/constants/variables.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:window_manager/window_manager.dart';

String getFileName(String dbPath) {
  if (dbPath.contains('/')) {
    return dbPath.split('/').last;
  } else if (dbPath.contains('\\')) {
    return dbPath.split('\\').last;
  } else {
    return dbPath;
  }
}

Map<IconData, VoidCallback?> windowControls({bool showSettings = false}) {
  final Map<IconData, VoidCallback?> buttons = {};
  if (showSettings) {
    buttons[Icons.settings] = () => Get.toNamed(ROUTES.settings);
  }
  if (isDesktop) {
    buttons[Icons.minimize] = () async {
      await windowManager.minimize();
    };
    buttons[Icons.rectangle_outlined] = () async {
      final isMaximized = await windowManager.isMaximized();
      if (isMaximized) {
        await windowManager.restore();
      } else {
        await windowManager.maximize();
      }
    };
    buttons[Icons.close] = () async {
      await windowManager.close();
    };
  }
  return buttons;
}
