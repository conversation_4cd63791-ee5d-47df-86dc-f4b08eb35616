#!/bin/sh

# Clear the terminal screen
clear

# Update Homebrew
echo "Updating Homebrew..."
if ! brew update; then
    echo "Failed to update homebrew. Exiting."
    exit 1
else
    brew upgrade
fi

# Copy the original Python assets zip for backup
# echo "Copying python.zip to python-clone.zip..."
# if ! cp assets/python.zip assets/python-clone.zip; then
#     echo "Failed to copy python.zip. Exiting."
#     exit 1
# else
# 	# Remove unnecessary architecture directories from the Python zip file
#     echo "Removing unwanted architectures from python.zip..."
#     if ! zip -d assets/python.zip "site-packages/arm64-v8a/*" "site-packages/armeabi-v7a/*" "site-packages/x86/*" "site-packages/x86_64/*"; then
#         echo "Failed to remove architectures from python.zip. Exiting."
#         exit 1
#     fi
# fi

# Upgrade flutter
echo "Updating shorebird..."
if ! flutter upgrade; then
    echo "shorebird upgrade"
    exit 1
fi

# Run Flutter package manager
echo "Running flutter pub get..."
if ! flutter pub get; then
    echo "Failed to run 'flutter pub get'. Exiting."
    exit 1
fi

# Upgrade shorebird
echo "Updating shorebird..."
if ! shorebird upgrade; then
    echo "shorebird upgrade"
    exit 1
fi

# Build the iOS app without code signing
echo "Building iOS app..."
if ! shorebird patch ios -- --no-codesign; then
    echo "iOS build failed. Exiting."
    exit 1
fi

# Clean up the old python.zip and replace it with the cloned version
# echo "Cleaning up the old python.zip..."
# if ! rm assets/python.zip; then
#     echo "Failed to delete the original python.zip."
# else
# 	echo "Moving the cloned python.zip back to the original name..."
# 	if ! mv assets/python-clone.zip assets/python.zip; then
# 		echo "Failed to move python-clone.zip to python.zip."
# 	fi
# fi

echo "Build process completed successfully."
