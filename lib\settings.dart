import 'dart:developer';
import 'package:dankware/constants/colors.dart';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/functions/common.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';

class Settings extends StatelessWidget {
  Settings({super.key});

  final colorSchemeController = Get.find<ColorSchemeController>();

  TextButton fgButton(String color) {
    final fgColors = getFgColors(color);
    return TextButton(
        onPressed: () {
          colorSchemeController.updateColorScheme(ColorSchemeNotifier(fgColors[0], fgColors[1], colorSchemeController.colorScheme.value.bgColor, colorSchemeController.colorScheme.value.bgColor2, colorSchemeController.colorScheme.value.bgColor3));
          storage.write('fgColor', color);
          log('[theme] updated fg: $color');
        },
        style: ButtonStyle(
          fixedSize: WidgetStatePropertyAll(Size(50, 50)),
          backgroundColor: WidgetStatePropertyAll(fgColors[0]),
          shape: WidgetStatePropertyAll<RoundedRectangleBorder>(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(ten)),
          ),
        ),
        child: SizedBox());
  }

  TextButton bgButton(String color) {
    final bgColors = getBgColors(color);
    return TextButton(
        onPressed: () {
          colorSchemeController.updateColorScheme(ColorSchemeNotifier(colorSchemeController.colorScheme.value.fgColor, colorSchemeController.colorScheme.value.fgColor2, bgColors[0], bgColors[1], bgColors[2]));
          storage.write('bgColor', color);
          log('[theme] updated bg: $color');
        },
        style: ButtonStyle(
          fixedSize: WidgetStatePropertyAll(Size(50, 50)),
          backgroundColor: WidgetStatePropertyAll(bgColors[0]),
          shape: WidgetStatePropertyAll<RoundedRectangleBorder>(RoundedRectangleBorder(borderRadius: BorderRadius.circular(ten))),
        ),
        child: SizedBox());
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final colorScheme = colorSchemeController.colorScheme.value;
      return Scaffold(
          extendBodyBehindAppBar: trueValue,
          appBar: transparentAppBar('Settings', windowControls()),
          body: Padding(
            padding: EdgeInsets.symmetric(vertical: fifteen, horizontal: fifteen),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text('Foreground', style: TextStyle(fontSize: twenty)),
                  SizedBox(height: fifteen),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: ten,
                    children: [
                      fgButton('red'),
                      fgButton('deepOrange'),
                      fgButton('orange'),
                      fgButton('amber'),
                    ],
                  ),
                  SizedBox(height: ten),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: ten,
                    children: [
                      fgButton('yellow'),
                      fgButton('lime'),
                      fgButton('lightGreen'),
                      fgButton('green'),
                    ],
                  ),
                  SizedBox(height: ten),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: ten,
                    children: [
                      fgButton('teal'),
                      fgButton('cyan'),
                      fgButton('lightBlue'),
                      fgButton('blue'),
                    ],
                  ),
                  SizedBox(height: ten),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [fgButton('indigo'), SizedBox(width: ten), fgButton('deepPurple'), SizedBox(width: ten), fgButton('purple'), SizedBox(width: ten), fgButton('pink')],
                  ),
                  SizedBox(height: twenty),
                  Text('Background', style: TextStyle(fontSize: twenty)),
                  SizedBox(height: fifteen),
                  bgButton((colorScheme.bgColor == Colors.black) || (colorScheme.bgColor == Colors.black.withAlpha(oneFifty)) ? 'white' : 'black')
                ],
              ),
            ),
          ));
    });
  }
}
