@echo off
setlocal enabledelayedexpansion

cls

:: Copy the original Python assets zip for backup
echo Copying python.zip to python-clone.zip...
copy assets\python.zip assets\python-clone.zip || (
    echo Failed to copy python.zip. Exiting.
    exit /b 1
)

:: Expand the Python archive and clean up unnecessary architectures
echo Expanding python.zip and removing unwanted architectures...
powershell -command ^
    "Expand-Archive -Path 'assets\python.zip' -DestinationPath 'assets\temp'; " ^
    "Remove-Item -Path 'assets\temp\site-packages\iphoneos.arm64\*' -Recurse -Force; " ^
    "Remove-Item -Path 'assets\temp\site-packages\iphonesimulator.arm64\*' -Recurse -Force; " ^
    "Remove-Item -Path 'assets\temp\site-packages\iphonesimulator.x86_64\*' -Recurse -Force; " ^
    "Remove-Item -Path 'assets\temp\site-packages\x86\*' -Recurse -Force; " ^
    "Remove-Item -Path 'assets\temp\site-packages\x86_64\*' -Recurse -Force; " ^
    "Compress-Archive -Path 'assets\temp\*' -DestinationPath 'assets\python.zip'; " ^
    "Remove-Item -Path 'assets\temp' -Recurse" 

if %errorlevel% neq 0 (
    echo Failed during the PowerShell commands. Exiting.
    exit /b 1
)

:: Ensure that flutter commands can be found
echo Running flutter pub get...
flutter pub get || (
    echo Failed to run 'flutter pub get'. Exiting.
    exit /b 1
)

:: Build the APK
echo Building APK...
flutter build apk || (
    echo APK build failed. Exiting.
    exit /b 1
)

:: Clean up the original python.zip and replace it with the newly created one
echo Cleaning up the old python.zip...
del assets\python.zip || (
    echo Failed to delete the original python.zip.
	echo Moving the cloned python.zip back to the original name...
    move /Y assets\python-clone.zip assets\python.zip || (
        echo Failed to move python-clone.zip to python.zip.
    )
)

:: Move the APK to the desired location
echo Moving the built APK to the target directory...
move /Y build\app\outputs\flutter-apk\app-release.apk ..\..\..\..\builds\dankware.apk || (
    echo Failed to move the APK to the target directory. Exiting.
    exit /b 1
)

echo Build process completed successfully.
