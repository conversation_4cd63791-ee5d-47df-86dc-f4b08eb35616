name: build-apk

on:
  workflow_dispatch:

jobs:
  build-apk:
    name: 🎉 Build APK
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          architecture: x64
          cache: true

      - name: Cache Android SDK
        uses: actions/cache@v4
        with:
          path: /usr/local/lib/android/sdk
          key: ${{ runner.os }}-android-sdk-${{ hashFiles('**/pubspec.lock') }}
          restore-keys: |
            ${{ runner.os }}-android-sdk-

      - name: Disable other platforms
        run: flutter config --no-enable-web --no-enable-linux-desktop --no-enable-macos-desktop --no-enable-windows-desktop --enable-android --no-enable-ios --no-enable-fuchsia --no-enable-custom-devices

      # - name: Upgrade Flutter packages
      #   run: flutter pub upgrade --major-versions

      # - name: Cleanup python.zip
      #   run: zip -d python.zip "site-packages/x86/*" "site-packages/x86_64/*" "site-packages/iphoneos.arm64/*" "site-packages/iphonesimulator.arm64/*" "site-packages/iphonesimulator.x86_64/*"
      #   working-directory: assets

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Build APK release
        run: flutter build apk

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: app-release.apk
          path: build/app/outputs/flutter-apk/app-release.apk
