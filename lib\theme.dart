import 'package:dankware/constants/variables.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'constants/colors.dart';
import 'constants/integers.dart';

Brightness _getBrightness(ColorSchemeNotifier colorScheme) {
  if ((colorScheme.bgColor == Colors.black) || (colorScheme.bgColor == Colors.black.withAlpha(oneFifty))) {
    return Brightness.dark;
  } else {
    return Brightness.light;
  }
}

ThemeData themeData() {
  final colorScheme = Get.find<ColorSchemeController>().colorScheme.value;
  return ThemeData(
    // General Theme Settings
    useMaterial3: trueValue,
    primaryColor: colorScheme.fgColor,
    primaryColorDark: colorScheme.fgColor,
    primaryColorLight: colorScheme.fgColor,
    brightness: _getBrightness(colorScheme),
    fontFamily: 'SourceCodePro',

    // Color Scheme
    colorScheme: ColorScheme(
      brightness: _getBrightness(colorScheme),
      primary: colorScheme.fgColor,
      onPrimary: colorScheme.bgColor,
      secondary: colorScheme.fgColor,
      onSecondary: colorScheme.bgColor,
      error: Colors.red,
      onError: Colors.white,
      surface: colorScheme.bgColor,
      onSurface: colorScheme.fgColor,
    ),

    // Scaffold and Background
    scaffoldBackgroundColor: colorScheme.bgColor,
    cardTheme: CardThemeData(color: colorScheme.bgColor),

    // AppBar
    appBarTheme: AppBarTheme(
        foregroundColor: colorScheme.fgColor,
        backgroundColor: Colors.transparent,
        centerTitle: trueValue,
        elevation: zero,
        titleTextStyle: TextStyle(
          fontFamily: 'StintUltraExpanded',
          fontWeight: FontWeight.bold,
          color: colorScheme.fgColor,
          fontSize: isMobile ? twentyFive : twenty,
        )),

    // Text Theme
    textTheme: TextTheme(
      displayLarge: TextStyle(color: colorScheme.fgColor2),
      displayMedium: TextStyle(color: colorScheme.fgColor2),
      displaySmall: TextStyle(color: colorScheme.fgColor2),
      headlineLarge: TextStyle(color: colorScheme.fgColor2),
      headlineMedium: TextStyle(color: colorScheme.fgColor2),
      headlineSmall: TextStyle(color: colorScheme.fgColor2),
      titleLarge: TextStyle(color: colorScheme.fgColor2),
      titleMedium: TextStyle(color: colorScheme.fgColor2),
      titleSmall: TextStyle(color: colorScheme.fgColor2),
      bodyLarge: TextStyle(color: colorScheme.fgColor2),
      bodyMedium: TextStyle(color: colorScheme.fgColor2),
      bodySmall: TextStyle(color: colorScheme.fgColor2),
      labelLarge: TextStyle(color: colorScheme.fgColor2),
      labelMedium: TextStyle(color: colorScheme.fgColor2),
      labelSmall: TextStyle(color: colorScheme.fgColor2),
    ),

    // Text Selection Theme
    textSelectionTheme: TextSelectionThemeData(cursorColor: colorScheme.fgColor, selectionColor: colorScheme.fgColor.withOpacity(0.35), selectionHandleColor: colorScheme.fgColor.withOpacity(0.35)),

    // Divider
    dividerColor: colorScheme.fgColor,
    dividerTheme: DividerThemeData(color: colorScheme.fgColor, thickness: two),

    // Button Themes
    outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
            foregroundColor: colorScheme.fgColor2,
            backgroundColor: colorScheme.bgColor,
            iconColor: colorScheme.fgColor2,
            overlayColor: colorScheme.fgColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(SIZES.borderRadius),
            ),
            side: BorderSide(
              color: colorScheme.fgColor,
              width: three,
            ))),
    elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
      foregroundColor: colorScheme.fgColor2,
      backgroundColor: colorScheme.bgColor2,
    )),
    textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
      foregroundColor: colorScheme.fgColor2,
      backgroundColor: colorScheme.bgColor2,
      iconColor: colorScheme.fgColor2,
    )),

    // Floating Action Button
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      foregroundColor: colorScheme.fgColor,
      backgroundColor: Colors.transparent,
      elevation: zero,
    ),

    // Input Decoration
    inputDecorationTheme: InputDecorationTheme(
      filled: trueValue,
      fillColor: colorScheme.bgColor2,
      labelStyle: TextStyle(color: colorScheme.fgColor),
      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: colorScheme.fgColor)),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: colorScheme.fgColor),
      ),
    ),

    // Dialog Theme
    dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.bgColor2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(twenty),
        )),

    // List Tile Theme
    listTileTheme: ListTileThemeData(textColor: colorScheme.fgColor),

    // Scrollbar Theme
    scrollbarTheme: ScrollbarThemeData(thumbColor: WidgetStatePropertyAll(colorScheme.fgColor)),

    // Progress Indicator Theme
    progressIndicatorTheme: ProgressIndicatorThemeData(color: colorScheme.fgColor),

    // Icon Theme
    iconTheme: IconThemeData(color: colorScheme.fgColor2, size: SIZES.iconS),

    // Tooltip Theme
    tooltipTheme: TooltipThemeData(
        waitDuration: Duration(seconds: 3),
        decoration: BoxDecoration(
          color: colorScheme.bgColor,
          borderRadius: BorderRadius.circular(five),
        ),
        textStyle: TextStyle(
          color: colorScheme.fgColor,
        )),
  );
}
