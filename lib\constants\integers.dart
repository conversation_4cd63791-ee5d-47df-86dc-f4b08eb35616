import 'package:dankware/constants/variables.dart';

const double zero = 0.0;
const double one = 1.0;
const double two = 2.0;
const double three = 3.0;
const double four = 4.0;
const double five = 5.0;

const double ten = 10.0;
const double fifteen = 15.0;
const double twenty = 20.0;
const double twentyFive = 25.0;
const double thirty = 30.0;
const double fourty = 40.0;
const double fifty = 50.0;
const double sixty = 60.0;
const double eighty = 80.0;
const double hundred = 100.0;
const int oneFifty = 150;

class SIZES {
  static final borderRadius = isMobile ? fifteen : ten;
  static final buttonHeight = isMobile ? eighty : fifty;
  static final buttonHeight2 = isMobile ? sixty : fourty;
  static final edgeInsets = isMobile ? ten : five;
  static final fontSizeL = isMobile ? twenty : fifteen;
  static final fontSizeM = isMobile ? fifteen : 12.0;
  static final iconL = isMobile ? fourty : thirty;
  static final iconS = isMobile ? twenty : fifteen;
}
