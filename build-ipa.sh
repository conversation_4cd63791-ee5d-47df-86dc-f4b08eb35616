#!/bin/sh

# Clear the terminal screen
clear

# Update Homebrew
echo "Updating Homebrew..."
if ! brew update; then
    echo "Failed to update homebrew. Exiting."
    exit 1
else
    brew upgrade
fi

# Copy the original Python assets zip for backup
# echo "Copying python.zip to python-clone.zip..."
# if ! cp assets/python.zip assets/python-clone.zip; then
#     echo "Failed to copy python.zip. Exiting."
#     exit 1
# else
#     # Remove unnecessary architecture directories from the Python zip file
#     echo "Removing unwanted architectures from python.zip..."
#     if ! zip -d assets/python.zip "site-packages/arm64-v8a/*" "site-packages/armeabi-v7a/*" "site-packages/x86/*" "site-packages/x86_64/*"; then
#         echo "Failed to remove architectures from python.zip. Exiting."
#         exit 1
#     fi
# fi

# Upgrade flutter
echo "Updating shorebird..."
if ! flutter upgrade; then
    echo "shorebird upgrade"
    exit 1
fi

# Run Flutter package manager
echo "Running flutter pub get..."
if ! flutter pub get; then
    echo "Failed to run 'flutter pub get'. Exiting."
    exit 1
fi

# Build the iOS app without code signing
echo "Building iOS app..."
if ! flutter build ios --release --no-codesign; then
    echo "iOS build failed. Exiting."
    exit 1
fi

# Clean up the old python.zip and replace it with the cloned version
# echo "Cleaning up the old python.zip..."
# if ! rm assets/python.zip; then
#     echo "Failed to delete the original python.zip."
# else
# 	echo "Moving the cloned python.zip back to the original name..."
# 	if ! mv assets/python-clone.zip assets/python.zip; then
# 		echo "Failed to move python-clone.zip to python.zip."
# 	fi
# fi

# Navigate to the build directory
cd build/ios || {
    echo "Failed to navigate to build/ios directory. Exiting."
    exit 1
}

# Rename the output directory and create the .ipa file
echo "Creating the IPA file..."
if ! mv iphoneos Payload; then
    echo "Failed to rename iphoneos to Payload. Exiting."
    exit 1
fi

if ! zip -qq -r -9 dankware.ipa Payload; then
    echo "Failed to create dankware.ipa. Exiting."
    exit 1
fi

# Move the payload back and move the .ipa to the desired directory
if ! mv Payload iphoneos; then
    echo "Failed to rename Payload back to iphoneos. Exiting."
    exit 1
fi

echo "Moving dankware.ipa to the builds directory..."
if ! mv -f dankware.ipa ../../builds/dankware.ipa; then
    echo "Failed to move dankware.ipa to ../../builds/. Exiting."
    exit 1
fi

# Push the changes to the remote repository
echo "Pushing the changes to the remote repository..."
if ! git add .; then
    echo "Failed to stage the changes. Exiting."
    exit 1
fi

if ! git commit -m "update dankware.ipa"; then
    echo "Failed to commit the changes. Exiting."
    exit 1
fi

if ! git push; then
    echo "Failed to push the changes to the remote repository. Exiting."
    exit 1
fi

# Fetch the latest changes from the remote repository
echo "Fetching the latest changes from the remote repository..."
if ! git fetch; then
    echo "Failed to fetch the latest changes from the remote repository. Exiting."
    exit 1
fi

echo "Build process completed successfully."
