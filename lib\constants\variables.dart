import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:get_storage/get_storage.dart';

const bool trueValue = true;
const bool falseValue = false;
final bool isMobile = Platform.isAndroid || Platform.isIOS ? true : false;
final bool isDesktop = Platform.isWindows || Platform.isMacOS || Platform.isLinux ? true : false;

final http.Client client = http.Client();
late Directory tempDir;
late GetStorage storage;

Future<void> initVariables() async {
  await GetStorage.init();
  storage = GetStorage();
  tempDir = await getTemporaryDirectory();
}
