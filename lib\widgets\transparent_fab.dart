import 'dart:ui';
import 'package:dankware/constants/integers.dart';
import 'package:flutter/material.dart';

ClipRRect transparentFAB(Function onPressed, String tooltip, dynamic child) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(ten),
    child: BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      child: FloatingActionButton(
          onPressed: () => onPressed(),
          tooltip: tooltip,
          heroTag: tooltip,
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ten)),
          child: child is String ? Text(child, style: TextStyle(fontSize: SIZES.fontSizeM)) : Icon(child)),
    ),
  );
}

ClipRRect extendedTransparentFAB(Function onPressed, String tooltip, String label, IconData icon) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(ten),
    child: BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      child: FloatingActionButton.extended(
        onPressed: () => onPressed(),
        tooltip: tooltip,
        heroTag: tooltip,
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ten)),
        label: Text(label, style: TextStyle(fontSize: SIZES.fontSizeM)),
        icon: Icon(icon),
      ),
    ),
  );
}
