import 'package:flutter/material.dart';
import 'package:dankware/constants/integers.dart';

AlertDialog customAlert(BuildContext context, String title, String content) {
  return AlertDialog(
    title: Text(title, style: const TextStyle(fontSize: twenty)),
    content: Text(content),
    actions: [
      OutlinedButton(
        onPressed: () => Navigator.of(context).pop(),
        child: const Text('OK'),
      )
    ],
  );
}
