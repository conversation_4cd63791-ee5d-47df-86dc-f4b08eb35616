import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/constants/strings.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:dankware/functions/common.dart';
import 'package:dankware/functions/open_url.dart';
import 'package:dankware/widgets/toast.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:dankware/widgets/transparent_fab.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

/// Controller for managing Reddit memes
class RandomRedditMemesController extends GetxController {
  final colorScheme = Get.find<ColorSchemeController>().colorScheme.value;
  RxList<Widget> imageSliders = <Widget>[].obs;
  RxList<dynamic> response = <dynamic>[].obs;

  @override
  void onInit() {
    super.onInit();
    getMemes();
  }

  /// Fetch memes from the API
  void getMemes() async {
    try {
      response.clear();
      final apiResponse = jsonDecode(
        await client.get(Uri.https('meme-api.com', '/gimme/5')).then((response) => response.body),
      )['memes'];
      response.addAll(apiResponse);
      log('[getMemes] Received ${response.length} memes!');
      loadMemes();
    } catch (e) {
      log('[getMemes] $e');
      showSnackbar('Random Reddit Memes Failed!');
    }
  }

  /// Build meme widgets for the slider
  void loadMemes() {
    imageSliders.clear();
    for (Map<String, dynamic> meme in response) {
      imageSliders.add(
        LayoutBuilder(
          builder: (context, constraints) {
            return ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: constraints.maxHeight,
                maxWidth: constraints.maxWidth,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (meme['title'].toLowerCase() != 'me_irl')
                    Text(
                      meme['title'],
                      style: TextStyle(fontSize: fifteen),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  SizedBox(height: ten),
                  Flexible(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(ten),
                      child: Image.network(
                        meme['url'],
                        fit: BoxFit.contain,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return SpinKitThreeBounce(
                            color: colorScheme.fgColor,
                            size: twenty,
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(Icons.error);
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: five),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: five,
                    children: [
                      TextButton.icon(
                        onPressed: () => openUrl(meme['postLink']),
                        label: Text(
                          'u/${meme['author']}',
                          style: TextStyle(fontSize: ten),
                        ),
                        icon: SvgPicture.asset(
                          ICONS.reddit,
                          width: twenty,
                          height: twenty,
                          colorFilter: ColorFilter.mode(colorScheme.fgColor, BlendMode.srcIn),
                        ),
                        style: ButtonStyle(
                          minimumSize: WidgetStatePropertyAll(Size(0, 45)),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(ten),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: colorScheme.bgColor2,
                          borderRadius: BorderRadius.circular(ten),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(ten),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.arrow_upward_outlined),
                              SizedBox(width: five),
                              Text('${meme['ups']}'),
                            ],
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          try {
                            final imageResponse = await client.get(Uri.parse(meme['url']));
                            final filePath = '${tempDir.path}/${meme['url'].split('/').last}';
                            final imageFile = File(filePath);
                            await imageFile.writeAsBytes(imageResponse.bodyBytes);
                            if (Platform.isWindows) {
                              await Process.run('start', [imageFile.path], runInShell: true);
                            } else if (Platform.isMacOS) {
                              await Process.run('open', [imageFile.path]);
                            } else {
                              await Share.shareXFiles([XFile(imageFile.path, mimeType: 'image/png')]);
                              if (await imageFile.exists()) {
                                await imageFile.delete();
                              }
                            }
                          } catch (e) {
                            log('[ShareMeme] $e');
                            showSnackbar('Failed to share meme!');
                          }
                        },
                        icon: Icon(Icons.share),
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(colorScheme.bgColor2),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(ten),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        ),
      );
    }
  }
}

class RandomRedditMemes extends StatelessWidget {
  final colorSchemeController = Get.find<ColorSchemeController>();
  final controller = Get.put(RandomRedditMemesController(), permanent: trueValue);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: trueValue,
      appBar: transparentAppBar('Reddit Memes', windowControls()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: fifteen),
        child: Center(
          child: Obx(() {
            final colorScheme = colorSchemeController.colorScheme.value;
            return controller.imageSliders.isEmpty
                ? SpinKitFadingFour(color: colorScheme.fgColor, size: hundred)
                : CarouselSlider(
                    options: CarouselOptions(
                      height: MediaQuery.of(context).size.height * 0.9,
                      enlargeCenterPage: trueValue,
                      scrollDirection: Axis.vertical,
                      autoPlay: trueValue,
                      autoPlayInterval: Duration(seconds: 10),
                    ),
                    items: controller.imageSliders,
                  );
          }),
        ),
      ),
      floatingActionButton: transparentFAB(controller.getMemes, 'Refresh', Icons.refresh),
    );
  }
}
