import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/variables.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:dankware/constants/colors.dart';

class ColorSchemeController extends GetxController {
  var colorScheme = ColorSchemeNotifier(
    Colors.redAccent.shade700,
    Colors.redAccent,
    isMobile ? Colors.black : Colors.black.withAlpha(oneFifty),
    Colors.grey.shade900,
    Colors.grey.shade800,
  ).obs;

  void updateColorScheme(ColorSchemeNotifier newColorScheme) {
    colorScheme.value = newColorScheme;
  }
}
