import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/strings.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/functions/common.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:flutter/material.dart';

class PythonModules extends StatelessWidget {
  const PythonModules({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(extendBodyBehindAppBar: trueValue, appBar: transparentAppBar(MODULES.pythonModules, windowControls()), body: Padding(padding: EdgeInsets.symmetric(horizontal: fifteen)));
  }
}
