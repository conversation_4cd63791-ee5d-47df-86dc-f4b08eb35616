import os
import time
import signal
from io import StringIO
from contextlib import redirect_stdout
from flask import Flask, request, Response
from concurrent.futures import ThreadPoolExecutor

port = 55001
print("Trying to run a socket server on:", port)

class PythonRunner:

    custom_commands = {
        "packages": """
print("Available packages:")
for _ in [__.name for __ in [_ for _ in pkgutil.iter_modules()]]:
    print(_)
""",

        "env": """
for key, val in os.environ.items():
    print(f'{key}: {val}')
""",

        "globals": """
for key, val in globals().items():
    if key == 'code': val = '<code skipped>'
    print(f'{key}: {val}')
""",

        "seedir": "sd.seedir(style='emoji')",

        "seedir-clean": "sd.seedir(style='emoji', exclude_folders=['site-packages', 'python_bundle', 'python_site_packages'])",

    }

    def __init__(self):
        self.__globals = {}
        self.__locals = {}
        exec("import os\nimport pkgutil\nimport emoji\nimport seedir as sd", self.__globals, self.__locals)

    def run(self, code):
        f = StringIO()
        if code.lower() in self.custom_commands:
            code = self.custom_commands[code.lower()]
        with redirect_stdout(f):
            try:
                exec(code, self.__globals, self.__locals)
                return f.getvalue().rstrip('\n'), 200
            except Exception as exc:
                return str(exc), 200 # 400

pr = PythonRunner()
app = Flask(__name__)
mimetype='text/plain'

@app.route("/")
def index():
    return Response('online!', mimetype=mimetype), 200

def terminate():
    time.sleep(0.25)
    os.kill(os.getpid(), signal.SIGINT)

@app.route('/shutdown', methods=['GET'])
def shutdown():
    ThreadPoolExecutor(1).submit(terminate)
    return Response("shutting down...", mimetype=mimetype), 200

@app.route("/execute", methods=["POST"])
def python_executor():
    if request.json:
        return pr.run(request.json["command"])
    return Response("JSON missing!", mimetype=mimetype), 400

try:
    app.run(port=port)
except SystemExit:
    pass
