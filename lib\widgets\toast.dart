import 'package:dankware/constants/integers.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

void showSnackbar(String text) {
  if (text.isEmpty) return;
  final colorScheme = Get.find<ColorSchemeController>().colorScheme.value;

  Get.snackbar('', '',
      titleText: Text(text, textAlign: TextAlign.center, style: TextStyle(fontWeight: FontWeight.bold)),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: colorScheme.bgColor,
      duration: Duration(seconds: 3),
      borderRadius: fifteen,
      margin: EdgeInsets.all(fifteen),
      barBlur: 12,
      maxWidth: 200);
}
