import 'dart:convert';
import 'dart:developer';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:serious_python/serious_python.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:dankware/constants/colors.dart';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/strings.dart';

class PythonExecutor extends StatefulWidget {
  const PythonExecutor({super.key});

  @override
  State<PythonExecutor> createState() => _PythonExecutorState();
}

class _PythonExecutorState extends State<PythonExecutor> {

  bool _active = falseValue;
  final _controller = TextEditingController();
  bool _processing = falseValue;
  final List<String> _results = [];
  final String _help = 'Available Commands:\n  - help\n  - clear\n  - packages\n  - env\n  - globals\n  - seedir\n  - seedir-clean';

  @override
  void dispose() {
    log('[PythonExecutor] dispose()');
    _shutdown();
    _controller.dispose();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    log('[PythonExecutor] initState()');
    SystemChrome.setPreferredOrientations([]);
    if (!_pythonStatus()) {
      SeriousPython.run("assets/python.zip", modulePaths: [
        "site-packages/arm64-v8a",
        "site-packages/armeabi-v7a",
        "site-packages/x86",
        "site-packages/x86_64",
        "site-packages/iphoneos.arm64",
        "site-packages/iphonesimulator.arm64",
        "site-packages/iphonesimulator.x86_64"
      ]);
      sharedPreferences.setBool('pythonStatus', trueValue);
    }
    _checkRunning();
  }

  bool _pythonStatus() {
    return sharedPreferences.getBool('pythonStatus') ?? falseValue;
  }

  void _shutdown() async {
    if (_pythonStatus()) {
      while (trueValue) {
        try {
          await http.get(Uri.parse("$urlPython/shutdown"));
          log('[PythonExecutor] Shutdown successful!');
          break;
        } catch (_) {
          log('[PythonExecutor] Shutdown failed! Sleeping 1s...');
          await Future.delayed(Duration(seconds: 1));
        }
      }
      sharedPreferences.setBool('pythonStatus', falseValue);
      SeriousPython.terminate();
    }
  }

  void _checkRunning() async {
    log('[PythonExecutor] Searching for flask server...');
    while (trueValue) {
      try {
        await http.get(Uri.parse(urlPython));
        log('[PythonExecutor] Flask server found!');
        setState(() {
          _active = trueValue;
          _results.add(_help);
        });
        break;
      } catch (_) {
        await Future.delayed(Duration(milliseconds: 250));
      }
    }
  }

  void _executeCommand() async {
    log('[PythonExecutor] command: ${_controller.text}');

    switch (_controller.text.toLowerCase()) {
      case "clear":
        setState(() {
          _results.clear();
          _controller.clear();
        });
        return;
      case "help":
        setState(() {
          _results.add(_help);
          _controller.clear();
        });
        return;
    }

    setState(() => _processing = trueValue);

    try {
      final response = await http.post(
        Uri.parse("$urlPython/execute"),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({"command": _controller.text}),
      );

      log('[PythonExecutor] result [${response.statusCode}]: ${response.body}');
      _results.add(response.body);
    } finally {
      setState(() {
        _processing = falseValue;
        _controller.clear();
      });
    }
  }

  Color _activeColor() {
    return !_active || _processing ? colorScheme.value.bgColor3 : colorScheme.value.fgColor;
  }

  Widget _buildInputArea() {
    return Padding(
      padding: EdgeInsets.all(ten),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: _controller,
              onTapOutside: (event) => FocusScope.of(context).unfocus(),
              decoration: InputDecoration(
                fillColor: colorScheme.value.bgColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(fifteen)),
                ),
                disabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: colorScheme.value.bgColor3, width: three),
                  borderRadius: BorderRadius.all(Radius.circular(fifteen)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: colorScheme.value.fgColor, width: three),
                  borderRadius: BorderRadius.all(Radius.circular(fifteen)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: colorScheme.value.fgColor, width: three),
                  borderRadius: BorderRadius.all(Radius.circular(fifteen)),
                ),
              ),
              spellCheckConfiguration: SpellCheckConfiguration.disabled(),
              smartQuotesType: SmartQuotesType.disabled,
              smartDashesType: SmartDashesType.disabled,
              keyboardType: TextInputType.multiline,
              enableSuggestions: falseValue,
              minLines: 1,
              maxLines: null,
              enabled: _active && !_processing,
            ),
          ),
          SizedBox(width: ten),
          SizedBox(
            height: 60,
            child: OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: _activeColor(), width: three),
              ),
              onPressed: (!_active || _processing || _controller.text.isEmpty)
                  ? null
                  : _executeCommand,
              child: Icon(Icons.play_arrow, color: _activeColor()),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: trueValue,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(40),
        child: transparentAppBar('Python Executor', {}),
      ),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                if (!_active || _processing)
                  Center(child: SpinKitFadingFour(color: colorScheme.value.fgColor, size: hundred)),
                Scrollbar(
                  child: ListView(
                    padding: EdgeInsets.only(top: hundred),
                    children: [Text(_results.join('\n'))],
                  ),
                ),
              ],
            ),
          ),
          _buildInputArea(),
          SizedBox(height: twentyFive),
        ],
      ),
    );
  }
}
