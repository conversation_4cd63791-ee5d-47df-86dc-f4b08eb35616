import 'dart:io';
import 'dart:convert';
import 'dart:developer';
import 'package:dankware/functions/common.dart';
import 'package:dankware/modules/ikwyd.dart';
import 'package:dankware/modules/python_modules.dart';
import 'package:dankware/settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:dankware/animations/blur_fade.dart';
import 'package:dankware/animations/border_beam.dart';
import 'package:dankware/constants/strings.dart';
import 'package:dankware/modules/random_reddit_memes.dart';
import 'package:dankware/animations/hyper_text.dart';
import 'package:dankware/theme.dart';
import 'package:dankware/modules/email_lookup.dart';
import 'package:dankware/widgets/transparent_appbar.dart';
import 'package:dankware/constants/colors.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/constants/integers.dart';
import 'package:get/get.dart';
import 'package:window_manager/window_manager.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)..badCertificateCallback = (X509Certificate cert, String host, int port) => trueValue;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initVariables();
  Get.put(ColorSchemeController(), permanent: trueValue);
  setColorScheme();
  HttpOverrides.global = MyHttpOverrides();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.leanBack);
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  if (isDesktop) {
    await windowManager.ensureInitialized();
    WindowOptions windowOptions = WindowOptions(
      size: Size(400, 620),
      minimumSize: Size(400, 620),
      center: trueValue,
      skipTaskbar: falseValue,
      titleBarStyle: TitleBarStyle.hidden,
    );
    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
    await Window.initialize();
    await Window.setEffect(
      effect: WindowEffect.acrylic,
      color: Get.find<ColorSchemeController>().colorScheme.value.bgColor,
    );
  }
  Get.put(HomeController(), permanent: trueValue);
  runApp(
    Obx(() {
      return GetMaterialApp(
        debugShowCheckedModeBanner: falseValue,
        defaultTransition: Transition.fadeIn,
        theme: themeData(),
        initialRoute: '/',
        getPages: [
          GetPage(name: '/', page: () => Home()),
          GetPage(name: ROUTES.settings, page: () => Settings()),
          GetPage(name: ROUTES.emailLookup, page: () => EmailLookup()),
          GetPage(name: ROUTES.emailLookupResults, page: () => EmailLookupResults()),
          GetPage(name: ROUTES.randomRedditMemes, page: () => RandomRedditMemes()),
          GetPage(name: ROUTES.ikwyd, page: () => IKWYD()),
          // GetPage(name: ROUTES.pythonModules, page: () => PythonModules()),
        ],
      );
    }),
  );
}

class HomeController extends GetxController {
  final RxMap<String, String> serverStats = {
    'Getting server stats...': '',
  }.obs;

  @override
  void onInit() {
    super.onInit();
    try {
      client.get(Uri.https(URLS.dankServer, '/counter', {'id': 'dankware', 'hit': 'true'}));
      client.get(Uri.https(URLS.dankServer, '/counter', {'id': 'mobile_app', 'hit': 'true'}));
    } catch (exc) {
      log('[initState] ERROR: $exc');
    }
    getServerStats();
  }

  void getServerStats() async {
    log('[main] Getting server stats...');
    try {
      final response = await client.get(Uri.https(URLS.dankServer, '/stats'));
      serverStats.value = (jsonDecode(response.body) as Map<String, dynamic>).map((key, value) => MapEntry(key, value.toString()));
      log('[main] Server stats: $serverStats');
    } catch (exc) {
      log('[getServerStats] ERROR: $exc');
      if (exc.toString().contains('SocketException')) {
        serverStats.value = {'Connection error!': ''};
      } else {
        serverStats.value = {exc.toString(): ''};
      }
    }
  }
}

class Home extends StatelessWidget {
  Home({super.key});

  final colorSchemeController = Get.find<ColorSchemeController>();
  final controller = Get.find<HomeController>();

  Widget _moduleButton(dynamic icon, String buttonText, String route) {
    return Obx(() {
      final colorScheme = colorSchemeController.colorScheme.value;
      return Padding(
        padding: const EdgeInsets.only(bottom: fifteen),
        child: BorderBeam(
          borderWidth: 6,
          borderRadius: BorderRadius.circular(SIZES.borderRadius),
          staticBorderColor: colorScheme.fgColor,
          colorFrom: colorScheme.fgColor2,
          colorTo: colorScheme.fgColor,
          child: SizedBox(
            height: SIZES.buttonHeight,
            width: double.infinity,
            child: TextButton.icon(
              onPressed: () => Get.toNamed(route),
              icon: icon is IconData
                  ? Icon(icon)
                  : SvgPicture.asset(
                      icon,
                      width: SIZES.iconS,
                      height: SIZES.iconS,
                      colorFilter: ColorFilter.mode(colorScheme.fgColor2, BlendMode.srcIn),
                    ),
              label: HyperText(text: buttonText, style: TextStyle(fontSize: SIZES.fontSizeL)),
              style: ButtonStyle(
                backgroundColor: WidgetStatePropertyAll(isMobile ? colorScheme.bgColor.withAlpha(oneFifty) : colorScheme.bgColor),
                shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SIZES.borderRadius),
                )),
              ),
            ),
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final colorScheme = colorSchemeController.colorScheme.value;
      return Scaffold(
        extendBodyBehindAppBar: trueValue,
        appBar: transparentAppBar('Modules', windowControls(showSettings: trueValue)),
        body: Padding(
          padding: EdgeInsets.symmetric(vertical: fifteen, horizontal: fifteen),
          child: ListView(
            children: [
              _moduleButton(Icons.search, MODULES.emailLookup, ROUTES.emailLookup),
              _moduleButton(ICONS.ikwyd, MODULES.ikwyd, ROUTES.ikwyd),
              _moduleButton(ICONS.reddit, MODULES.randomRedditMemes, ROUTES.randomRedditMemes),
              // _moduleButton(ICONS.python, MODULES.pythonModules, ROUTES.pythonModules),
              BorderBeam(
                borderWidth: 6,
                borderRadius: BorderRadius.circular(SIZES.borderRadius),
                staticBorderColor: colorScheme.fgColor,
                colorFrom: colorScheme.fgColor2,
                colorTo: colorScheme.fgColor,
                child: Container(
                  decoration: BoxDecoration(color: isMobile ? colorScheme.bgColor.withAlpha(oneFifty) : colorScheme.bgColor, borderRadius: BorderRadius.circular(SIZES.borderRadius)),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: fifteen, top: five),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [Icon(Icons.dns, size: SIZES.iconS), SizedBox(width: five), HyperText(text: 'dank.server', style: TextStyle(fontSize: SIZES.fontSizeL))],
                            ),
                            Padding(
                              padding: const EdgeInsets.only(right: 5),
                              child: IconButton(onPressed: controller.getServerStats, icon: Icon(Icons.refresh)),
                            )
                          ],
                        ),
                      ),
                      Obx(() => ListView.builder(
                            padding: EdgeInsets.symmetric(horizontal: fifteen),
                            shrinkWrap: trueValue,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: controller.serverStats.length,
                            itemBuilder: (context, index) {
                              final entry = controller.serverStats.entries.elementAt(index);
                              return BlurFade(
                                delay: Duration(milliseconds: index * 100),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    HyperText(text: entry.key.replaceAll('_', ' '), style: TextStyle(fontSize: SIZES.fontSizeM), key: ObjectKey('$index-${entry.key}')),
                                    Expanded(child: Divider(thickness: one, indent: ten, endIndent: ten)),
                                    HyperText(text: entry.value.toString(), style: TextStyle(fontSize: SIZES.fontSizeM), key: ObjectKey('$index-${entry.key}')),
                                  ],
                                ),
                              );
                            },
                          )),
                      SizedBox(height: fifteen),
                    ],
                  ),
                ),
              ),
              SizedBox(height: twenty),
              Center(child: HyperText(text: 'Dankware', style: TextStyle(fontSize: SIZES.fontSizeM))),
              Center(child: HyperText(text: 'v1.1.4', style: TextStyle(fontSize: SIZES.fontSizeM))),
            ],
          ),
        ),
      );
    });
  }
}
