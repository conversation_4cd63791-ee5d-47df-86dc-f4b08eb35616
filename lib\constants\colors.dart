import 'dart:developer';
import 'package:dankware/constants/integers.dart';
import 'package:dankware/constants/variables.dart';
import 'package:dankware/controllers/color_scheme_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ColorSchemeNotifier {
  ColorSchemeNotifier(this.fgColor, this.fgColor2, this.bgColor, this.bgColor2, this.bgColor3);

  Color bgColor;
  Color bgColor2;
  Color bgColor3;
  Color fgColor;
  Color fgColor2;
}

List getFgColors(String color) {
  switch (color) {
    case 'deepOrange':
      return [Colors.deepOrangeAccent.shade700, Colors.deepOrangeAccent];
    case 'orange':
      return [Colors.orangeAccent.shade700, Colors.orangeAccent];
    case 'amber':
      return [Colors.amberAccent.shade700, Colors.amberAccent];
    case 'yellow':
      return [Colors.yellowAccent.shade700, Colors.yellowAccent];
    case 'lime':
      return [Colors.limeAccent.shade700, Colors.limeAccent];
    case 'lightGreen':
      return [Colors.lightGreenAccent.shade700, Colors.lightGreenAccent];
    case 'green':
      return [Colors.greenAccent.shade700, Colors.greenAccent];
    case 'teal':
      return [Colors.tealAccent.shade700, Colors.tealAccent];
    case 'cyan':
      return [Colors.cyanAccent.shade700, Colors.cyanAccent];
    case 'lightBlue':
      return [Colors.lightBlueAccent.shade700, Colors.lightBlueAccent];
    case 'blue':
      return [Colors.blueAccent.shade700, Colors.blueAccent];
    case 'indigo':
      return [Colors.indigoAccent.shade700, Colors.indigoAccent];
    case 'deepPurple':
      return [Colors.deepPurpleAccent.shade700, Colors.deepPurpleAccent];
    case 'purple':
      return [Colors.purpleAccent.shade700, Colors.purpleAccent];
    case 'pink':
      return [Colors.pinkAccent.shade700, Colors.pinkAccent];
    default:
      return [Colors.redAccent.shade700, Colors.redAccent];
  }
}

List getBgColors(String color) {
  switch (color) {
    case 'white':
      if (isMobile) {
        return [Colors.white, Colors.grey.shade200, Colors.grey.shade100];
      }
      return [Colors.white.withAlpha(oneFifty), Colors.grey.shade200.withAlpha(oneFifty), Colors.grey.shade100.withAlpha(oneFifty)];
    default:
      if (isMobile) {
        return [Colors.black, Colors.grey.shade900, Colors.grey.shade800];
      }
      return [Colors.black.withAlpha(oneFifty), Colors.grey.shade900.withAlpha(oneFifty), Colors.grey.shade800.withAlpha(oneFifty)];
  }
}

void setColorScheme() {
  String color = storage.read('fgColor') ?? 'red';
  log('[theme] current fg: $color');
  final fgColors = getFgColors(color);
  color = storage.read('bgColor') ?? 'black';
  log('[theme] current bg: $color');
  final bgColors = getBgColors(color);
  Get.find<ColorSchemeController>().updateColorScheme(ColorSchemeNotifier(fgColors[0], fgColors[1], bgColors[0], bgColors[1], bgColors[2]));
}
