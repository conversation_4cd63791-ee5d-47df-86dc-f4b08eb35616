name: build-ipa

on:
  workflow_dispatch:

jobs:
  build-ipa:
    name: 🎉 Build IPA
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          architecture: x64
          cache: true

      - name: Disable other platforms
        run: flutter config --no-enable-web --no-enable-linux-desktop --no-enable-macos-desktop --no-enable-windows-desktop --no-enable-android --enable-ios --no-enable-fuchsia --no-enable-custom-devices

      # - name: Upgrade Flutter packages
      #   run: flutter pub upgrade --major-versions

      # - name: Update CocoaPods
      #   run: pod repo update
      #   working-directory: ios

      # - name: Cleanup python.zip
      #   run: zip -d python.zip "site-packages/arm64-v8a/*" "site-packages/armeabi-v7a/*" "site-packages/x86/*" "site-packages/x86_64/*"
      #   working-directory: assets

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Build iOS release
        run: flutter build ios --release --no-codesign

      - name: Rename Payload directory
        run: mv iphoneos Payload
        working-directory: build/ios

      - name: Zip output
        run: zip -qq -r -9 release.ipa Payload
        working-directory: build/ios

      - name: Rename Payload directory
        run: mv Payload iphoneos
        working-directory: build/ios

      - name: Upload regular ipa
        uses: actions/upload-artifact@v4
        with:
          name: release.ipa
          path: build/ios/release.ipa
